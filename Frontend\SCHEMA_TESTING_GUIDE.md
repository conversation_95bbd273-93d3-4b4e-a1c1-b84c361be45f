# JSON-LD Schema Testing Guide

## Overview
This guide provides instructions for testing the implemented JSON-LD structured data schemas to ensure they are properly rendered in the HTML source and can be crawled by search engines.

## 1. Homepage Schema Testing

### 1.1 Manual Testing
1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Visit the homepage:**
   - Navigate to `http://localhost:3000`
   - Right-click and select "View Page Source"
   - Search for `application/ld+json` in the source code

3. **Expected Results:**
   You should see two separate `<script type="application/ld+json">` tags:

   **Organization Schema:**
   ```html
   <script type="application/ld+json">
   {"@context":"https://schema.org","@type":"Organization","name":"TradeReply","url":"https://www.tradereply.com","logo":"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png","contactPoint":{"@type":"ContactPoint","url":"https://www.tradereply.com/help","contactType":"Customer Support","areaServed":"Global","availableLanguage":"English"},"sameAs":["https://www.facebook.com/TradeReply","https://www.instagram.com/tradereply","https://x.com/JoinTradeReply"]}
   </script>
   ```

   **WebSite Schema:**
   ```html
   <script type="application/ld+json">
   {"@context":"https://schema.org","@type":"WebSite","url":"https://www.tradereply.com/","name":"TradeReply"}
   </script>
   ```

### 1.2 Automated Testing
Create a simple test to verify schema presence:

```javascript
// tests/homepage-schema.test.js
import { JSDOM } from 'jsdom';

test('Homepage contains Organization and WebSite schemas', async () => {
  const response = await fetch('http://localhost:3000');
  const html = await response.text();
  const dom = new JSDOM(html);
  
  const scripts = dom.window.document.querySelectorAll('script[type="application/ld+json"]');
  expect(scripts.length).toBeGreaterThanOrEqual(2);
  
  const schemas = Array.from(scripts).map(script => JSON.parse(script.textContent));
  const organizationSchema = schemas.find(schema => schema['@type'] === 'Organization');
  const websiteSchema = schemas.find(schema => schema['@type'] === 'WebSite');
  
  expect(organizationSchema).toBeDefined();
  expect(organizationSchema.name).toBe('TradeReply');
  expect(websiteSchema).toBeDefined();
  expect(websiteSchema.url).toBe('https://www.tradereply.com/');
});
```

## 2. Blog Article Schema Testing

### 2.1 Prerequisites
**Note:** Blog article schema testing requires backend implementation. Until the backend changes are implemented, the schema will use fallback data.

### 2.2 Manual Testing (After Backend Implementation)
1. **Create a test blog article** with schema fields populated:
   - `schema_article_body`: 500-600 character summary
   - `schema_keywords`: 5-8 comma-separated keywords

2. **Visit the blog article page:**
   - Navigate to `http://localhost:3000/blog/[article-slug]`
   - Right-click and select "View Page Source"
   - Search for `application/ld+json` in the source code

3. **Expected Results:**
   You should see a `<script type="application/ld+json">` tag with BlogPosting schema:

   ```html
   <script type="application/ld+json">
   {"@context":"https://schema.org","@type":"BlogPosting","mainEntityOfPage":{"@type":"WebPage","@id":"https://www.tradereply.com/blog/article-slug"},"headline":"Article Title","description":"Article summary","image":"https://cdn.tradereply.com/path/to/image.jpg","author":{"@type":"Organization","name":"TradeReply"},"publisher":{"@type":"Organization","name":"TradeReply","logo":{"@type":"ImageObject","url":"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"}},"datePublished":"2025-01-01T12:00:00.000Z","dateModified":"2025-01-01T12:00:00.000Z","articleBody":"500-600 character summary for JSON-LD schema...","keywords":"trading, finance, stocks, crypto, investing"}
   </script>
   ```

### 2.3 Testing with Fallback Data (Current State)
Since backend implementation is pending, the schema will use fallback data:
- `articleBody` will fallback to `blog.summary`
- `keywords` will be empty string
- Other fields will populate from existing blog data

## 3. SEO Validation Tools

### 3.1 Google Rich Results Test
1. **Visit:** https://search.google.com/test/rich-results
2. **Enter your page URL** or paste the HTML source
3. **Verify** that the structured data is detected and valid

### 3.2 Schema.org Validator
1. **Visit:** https://validator.schema.org/
2. **Paste the JSON-LD code** from your page source
3. **Verify** that the schema validates without errors

### 3.3 Google Search Console
After deployment:
1. **Submit your pages** to Google Search Console
2. **Monitor** the "Enhancements" section for structured data reports
3. **Check** for any errors or warnings

## 4. Browser Developer Tools Testing

### 4.1 Chrome DevTools
1. **Open DevTools** (F12)
2. **Go to Elements tab**
3. **Search** for `application/ld+json`
4. **Verify** the JSON structure is properly formatted

### 4.2 Lighthouse SEO Audit
1. **Open DevTools** (F12)
2. **Go to Lighthouse tab**
3. **Run SEO audit**
4. **Check** for structured data recommendations

## 5. Common Issues and Troubleshooting

### 5.1 Schema Not Appearing
**Possible Causes:**
- Component not imported correctly
- Schema generation function returning null
- SSR not working properly

**Solutions:**
- Check browser console for JavaScript errors
- Verify imports in page components
- Ensure data is available during server-side rendering

### 5.2 Invalid JSON Structure
**Possible Causes:**
- Special characters not properly escaped
- Undefined values in schema data
- Circular references in objects

**Solutions:**
- Use `JSON.stringify()` with proper error handling
- Validate all data before schema generation
- Check for null/undefined values

### 5.3 Schema Validation Errors
**Common Issues:**
- Missing required fields
- Incorrect date formats
- Invalid URL formats

**Solutions:**
- Use the `formatDateToISO()` utility function
- Validate URLs before including in schema
- Check schema.org documentation for required fields

## 6. Performance Considerations

### 6.1 Schema Size
- Keep JSON-LD schemas concise
- Avoid including unnecessary data
- Monitor page load impact

### 6.2 SSR Performance
- Ensure schema generation doesn't slow down server rendering
- Consider caching for frequently accessed pages
- Monitor server response times

## 7. Deployment Checklist

### 7.1 Pre-deployment
- [ ] All schemas validate with schema.org validator
- [ ] Google Rich Results Test passes
- [ ] Manual testing completed on all target pages
- [ ] Performance impact assessed

### 7.2 Post-deployment
- [ ] Verify schemas appear in production HTML source
- [ ] Submit updated sitemap to Google Search Console
- [ ] Monitor for crawl errors
- [ ] Check structured data reports in Search Console

## 8. Monitoring and Maintenance

### 8.1 Regular Checks
- Monthly validation of schema markup
- Monitor Google Search Console for structured data errors
- Update schemas when content structure changes

### 8.2 Schema Updates
- Keep schemas updated with latest schema.org specifications
- Test schema changes in staging environment
- Document any schema modifications

## 9. Browser Compatibility

The implemented JSON-LD schemas are compatible with:
- All modern browsers (Chrome, Firefox, Safari, Edge)
- Search engine crawlers (Google, Bing, etc.)
- Social media crawlers (Facebook, Twitter, etc.)

No special polyfills or fallbacks are required for JSON-LD structured data.
