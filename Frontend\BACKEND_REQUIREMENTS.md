# Backend Requirements for JSON-LD Schema Implementation

## Overview
This document outlines the required backend changes to support the JSON-LD structured data schemas implemented in the frontend. The frontend implementation is complete and ready to consume these new fields once the backend is updated.

## 1. Database Schema Changes

### 1.1 Articles Table Migration
Create a new migration to add schema-specific fields to the articles table:

**Migration File:** `database/migrations/YYYY_MM_DD_HHMMSS_add_schema_fields_to_articles_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            // Schema-specific fields for JSON-LD structured data
            $table->text('schema_article_body')->nullable()->after('summary');
            $table->string('schema_keywords', 500)->nullable()->after('schema_article_body');
        });
    }

    public function down(): void
    {
        Schema::table('articles', function (Blueprint $table) {
            $table->dropColumn(['schema_article_body', 'schema_keywords']);
        });
    }
};
```

**Field Specifications:**
- `schema_article_body`: TEXT field for 500-600 character article summary for JSON-LD
- `schema_keywords`: VARCHAR(500) field for comma-separated SEO keywords

## 2. Model Updates

### 2.1 Article Model (`app/Models/Article.php`)

**Add to fillable array:**
```php
protected $fillable = [
    // ... existing fields
    'schema_article_body',
    'schema_keywords'
];
```

**Optional: Add accessor methods for data validation:**
```php
/**
 * Get cleaned schema keywords
 */
public function getSchemaKeywordsCleanAttribute()
{
    if (!$this->schema_keywords) {
        return '';
    }
    
    return collect(explode(',', $this->schema_keywords))
        ->map(fn($keyword) => trim($keyword))
        ->filter(fn($keyword) => !empty($keyword))
        ->implode(', ');
}

/**
 * Validate schema article body length
 */
public function getSchemaArticleBodyValidAttribute()
{
    $length = strlen($this->schema_article_body ?? '');
    return $length >= 500 && $length <= 600;
}
```

## 3. API Resource Updates

### 3.1 ArticleResource (`app/Http/Resources/ArticleResource.php`)

**Add new fields to the resource array:**
```php
public function toArray(Request $request): array
{
    return [
        // ... existing fields
        'schema_article_body' => $this->schema_article_body ?? '',
        'schema_keywords' => $this->schema_keywords ?? '',
    ];
}
```

## 4. Controller Updates

### 4.1 ArticleController (`app/Http/Controllers/Admin/ArticleController.php`)

**Update store method:**
```php
public function store(Request $request)
{
    $data = $request->only([
        // ... existing fields
        'schema_article_body',
        'schema_keywords',
    ]);
    
    // ... rest of store logic
}
```

**Update update method:**
```php
public function update(Request $request, $slug)
{
    $data = $request->only([
        // ... existing fields
        'schema_article_body',
        'schema_keywords',
    ]);
    
    // ... rest of update logic
}
```

## 5. Validation Rules

### 5.1 Article Request Validation

**Create or update:** `app/Http/Requests/ArticleRequest.php`

```php
public function rules(): array
{
    return [
        // ... existing rules
        'schema_article_body' => [
            'nullable',
            'string',
            'min:500',
            'max:600'
        ],
        'schema_keywords' => [
            'nullable',
            'string',
            'max:500',
            function ($attribute, $value, $fail) {
                if ($value) {
                    $keywords = array_filter(array_map('trim', explode(',', $value)));
                    if (count($keywords) < 5 || count($keywords) > 8) {
                        $fail('The schema keywords must contain between 5 and 8 comma-separated keywords.');
                    }
                }
            }
        ],
    ];
}
```

**Custom error messages:**
```php
public function messages(): array
{
    return [
        'schema_article_body.min' => 'The schema article body must be at least 500 characters for SEO optimization.',
        'schema_article_body.max' => 'The schema article body cannot exceed 600 characters.',
        'schema_keywords.max' => 'The schema keywords field cannot exceed 500 characters.',
    ];
}
```

## 6. API Endpoint Considerations

### 6.1 Existing Endpoints to Update

**Blog Detail Endpoint:** `/api/v1/article/blog/{slug}`
- Ensure the response includes the new schema fields
- Verify the ArticleResource is being used consistently

**Admin Blog Management Endpoints:**
- `POST /api/v1/super-admin/articles/store`
- `PUT /api/v1/super-admin/articles/{slug}`
- `GET /api/v1/super-admin/articles/list/blog`

### 6.2 Response Format Verification

Ensure the API response includes the new fields:
```json
{
  "data": {
    "id": 1,
    "title": "Blog Title",
    "summary": "Blog summary",
    "schema_article_body": "500-600 character summary for JSON-LD schema...",
    "schema_keywords": "trading, finance, stocks, crypto, investing",
    "created_at": "2025-01-01T12:00:00Z",
    "updated_at": "2025-01-01T12:00:00Z"
  }
}
```

## 7. Database Seeding (Optional)

### 7.1 Update Existing Articles

**Create a seeder to populate existing articles:**
```php
// database/seeders/UpdateArticlesSchemaFieldsSeeder.php

public function run()
{
    Article::where('type', 'blog')
        ->whereNull('schema_article_body')
        ->chunk(100, function ($articles) {
            foreach ($articles as $article) {
                $article->update([
                    'schema_article_body' => Str::limit($article->summary, 550),
                    'schema_keywords' => 'trading, finance, investment, strategy, analysis'
                ]);
            }
        });
}
```

## 8. Testing Requirements

### 8.1 Unit Tests

**Test the new model fields:**
```php
// tests/Unit/ArticleTest.php

public function test_article_can_store_schema_fields()
{
    $article = Article::factory()->create([
        'schema_article_body' => str_repeat('a', 550),
        'schema_keywords' => 'trading, finance, stocks, crypto, investing'
    ]);
    
    $this->assertNotNull($article->schema_article_body);
    $this->assertNotNull($article->schema_keywords);
}
```

### 8.2 Feature Tests

**Test API endpoints:**
```php
// tests/Feature/ArticleApiTest.php

public function test_blog_detail_includes_schema_fields()
{
    $article = Article::factory()->create([
        'type' => 'blog',
        'schema_article_body' => 'Test schema body content...',
        'schema_keywords' => 'test, keywords'
    ]);
    
    $response = $this->get("/api/v1/article/blog/{$article->slug}");
    
    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'schema_article_body',
                'schema_keywords'
            ]
        ]);
}
```

## 9. Admin Interface Backend Support

### 9.1 Form Handling

Ensure the admin endpoints properly handle the new fields in form submissions:
- Validate field lengths and formats
- Store the fields in the database
- Return appropriate error messages for validation failures

### 9.2 Listing/Editing Support

Update admin listing and editing endpoints to include the new fields for proper form population during edit operations.

## 10. Priority Implementation Order

1. **High Priority:**
   - Database migration
   - Model fillable fields update
   - ArticleResource update
   - Controller store/update methods

2. **Medium Priority:**
   - Validation rules
   - Error message customization
   - API endpoint testing

3. **Low Priority:**
   - Data seeding for existing articles
   - Unit/feature tests
   - Performance optimization

## 11. Deployment Notes

- Run the migration in production: `php artisan migrate`
- Clear application cache: `php artisan cache:clear`
- Update API documentation if applicable
- Monitor for any performance impacts on article queries

## 12. Frontend Integration Points

The frontend is already implemented and expects:
- `schema_article_body` field in blog API responses
- `schema_keywords` field in blog API responses
- Proper handling of these fields in admin form submissions
- ISO 8601 formatted dates in `created_at` and `updated_at` fields

Once these backend changes are implemented, the JSON-LD schemas will automatically populate with the correct data and appear in the HTML source for SEO crawling.
