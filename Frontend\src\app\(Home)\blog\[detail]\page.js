import { unstable_noStore as noStore } from 'next/cache';
import HomeLayout from "@/Layouts/HomeLayout";
import RecentPost from "@/Components/common/Home/RecentPost";
import { Container } from "react-bootstrap";
import Link from "next/link";
import "../../../../css/Home/BlogDetail.scss";
import CustomBreadcrumb from "@/Components/UI/CustomBreadcrumb";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime);
import BlogContent from "./components/BlogContent";
import DOMPurify from "dompurify";

async function fetchBlog(detail) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/blog/${detail}`);

  if (!res.ok) {
      throw new Error(`API error: ${res.status}`);
  }

  return res.json();
}

export default async function BlogDetail({ params }) {
  noStore();

  const resolvedParams = await params;
  const detail = resolvedParams.detail;

  const response = await fetchBlog(detail);

   return (
      <BlogContent
        response={response}
      />
    );
}

export async function generateMetadata({ params }) {
  noStore();
  const resolvedParams = await params;
  const detail = resolvedParams.detail;
  const response = await fetchBlog(detail);
  const blog = response.data;
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;

 return {
   title: blog.title
     ? `${blog.title} | TradeReply Blog`
     : 'TradeReply Blog | Insights & Strategies for Traders',
   description: blog.summary || 'Explore insights on TradeReply.',
   robots: "index, follow",
   openGraph: {
     title: `${blog.title} | TradeReply Blog`,
     description: blog?.summary,
     siteName: 'TradeReply',
     type: 'article',
     images: [
       {
         url: blog?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed
         width: 1200,
         height: 630,
       },
     ],
     locale: 'en_US',
   },
   twitter: {
     title: `${blog?.title} | TradeReply Blog`,
     description: blog?.summary,
     site: '@JoinTradeReply',
     images: [blog?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed
   },
   icons: {
         icon: [
           {
             url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
             type: "image/x-icon",
           },
           {
             url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
             type: "image/svg+xml",
           },
         ],
       },
 };
}
