/**
 * JsonLdSchema Component
 * 
 * Renders JSON-LD structured data schemas for SEO purposes.
 * Each schema is rendered in its own separate <script type="application/ld+json"> tag
 * to ensure proper search engine crawling and indexing.
 * 
 * Usage:
 * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />
 */

export default function JsonLdSchema({ schemas = [] }) {
  if (!schemas || schemas.length === 0) {
    return null;
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema, null, 0)
          }}
        />
      ))}
    </>
  );
}

/**
 * Homepage Schema Generators
 */

export const generateOrganizationSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "TradeReply",
    "url": "https://www.tradereply.com",
    "logo": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "url": "https://www.tradereply.com/help",
      "contactType": "Customer Support",
      "areaServed": "Global",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://www.facebook.com/TradeReply",
      "https://www.instagram.com/tradereply",
      "https://x.com/JoinTradeReply"
    ]
  };
};

export const generateWebsiteSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://www.tradereply.com/",
    "name": "TradeReply"
  };
};

/**
 * Blog Article Schema Generator
 */

export const generateBlogPostingSchema = ({
  canonicalUrl,
  headline,
  description,
  imageUrl,
  datePublished,
  dateModified,
  articleBody,
  keywords
}) => {
  // Only generate schema if required fields are present
  if (!canonicalUrl || !headline) {
    return null;
  }

  return {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    },
    "headline": headline,
    "description": description || "",
    "image": imageUrl || "",
    "author": {
      "@type": "Organization",
      "name": "TradeReply"
    },
    "publisher": {
      "@type": "Organization",
      "name": "TradeReply",
      "logo": {
        "@type": "ImageObject",
        "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
      }
    },
    "datePublished": datePublished || "",
    "dateModified": dateModified || datePublished || "",
    "articleBody": articleBody || description || "",
    "keywords": keywords || ""
  };
};

/**
 * Utility function to format dates to ISO 8601 format
 * Converts various date formats to ISO 8601 string format required by schema.org
 * 
 * @param {string|Date} date - Date to format
 * @returns {string|null} - ISO 8601 formatted date string or null if invalid
 */
export const formatDateToISO = (date) => {
  if (!date) return null;
  
  try {
    // Handle different date formats
    let dateObj;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return null;
    }
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return null;
    }
    
    return dateObj.toISOString();
  } catch (error) {
    console.warn('Error formatting date to ISO:', error);
    return null;
  }
};

/**
 * Utility function to safely extract blog slug from URL or data
 * 
 * @param {Object} blog - Blog data object
 * @returns {string} - Clean blog slug
 */
export const getBlogSlug = (blog) => {
  if (!blog) return '';
  
  // If slug exists, use it directly
  if (blog.slug) {
    return blog.slug;
  }
  
  // Fallback: generate slug from title
  if (blog.title) {
    return blog.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  
  return '';
};

/**
 * Utility function to validate and clean keywords string
 * 
 * @param {string} keywords - Comma-separated keywords
 * @returns {string} - Cleaned keywords string
 */
export const cleanKeywords = (keywords) => {
  if (!keywords || typeof keywords !== 'string') {
    return '';
  }
  
  return keywords
    .split(',')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
    .join(', ');
};
