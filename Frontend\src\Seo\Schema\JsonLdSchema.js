/**
 * JsonLdSchema Component
 * 
 * Renders JSON-LD structured data schemas for SEO purposes.
 * Each schema is rendered in its own separate <script type="application/ld+json"> tag
 * to ensure proper search engine crawling and indexing.
 * 
 * Usage:
 * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />
 */

export default function JsonLdSchema({ schemas = [] }) {
  if (!schemas || schemas.length === 0) {
    return null;
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema, null, 0)
          }}
        />
      ))}
    </>
  );
}

/**
 * Homepage Schema Generators
 */

export const generateOrganizationSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "TradeReply",
    "url": "https://www.tradereply.com",
    "logo": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "url": "https://www.tradereply.com/help",
      "contactType": "Customer Support",
      "areaServed": "Global",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://www.facebook.com/TradeReply",
      "https://www.instagram.com/tradereply",
      "https://x.com/JoinTradeReply"
    ]
  };
};

export const generateWebsiteSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://www.tradereply.com/",
    "name": "TradeReply"
  };
};

/**
 * Blog Article Schema Generator
 */

export const generateBlogPostingSchema = ({
  canonicalUrl,
  headline,
  description,
  imageUrl,
  datePublished,
  dateModified,
  articleBody,
  keywords
}) => {
  // Only generate schema if required fields are present
  if (!canonicalUrl || !headline) {
    return null;
  }

  return {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    },
    "headline": headline,
    "description": description || "",
    "image": imageUrl || "",
    "author": {
      "@type": "Organization",
      "name": "TradeReply"
    },
    "publisher": {
      "@type": "Organization",
      "name": "TradeReply",
      "logo": {
        "@type": "ImageObject",
        "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
      }
    },
    "datePublished": datePublished || "",
    "dateModified": dateModified || datePublished || "",
    "articleBody": articleBody || description || "",
    "keywords": keywords || ""
  };
};

/**
 * Utility function to format dates to ISO 8601 format
 * Converts various date formats to ISO 8601 string format required by schema.org
 * 
 * @param {string|Date} date - Date to format
 * @returns {string|null} - ISO 8601 formatted date string or null if invalid
 */
export const formatDateToISO = (date) => {
  if (!date) return null;
  
  try {
    // Handle different date formats
    let dateObj;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return null;
    }
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return null;
    }
    
    return dateObj.toISOString();
  } catch (error) {
    console.warn('Error formatting date to ISO:', error);
    return null;
  }
};

/**
 * Utility function to safely extract blog slug from URL or data
 * 
 * @param {Object} blog - Blog data object
 * @returns {string} - Clean blog slug
 */
export const getBlogSlug = (blog) => {
  if (!blog) return '';
  
  // If slug exists, use it directly
  if (blog.slug) {
    return blog.slug;
  }
  
  // Fallback: generate slug from title
  if (blog.title) {
    return blog.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  
  return '';
};

/**
 * Utility function to validate and clean keywords string
 *
 * @param {string} keywords - Comma-separated keywords
 * @returns {string} - Cleaned keywords string
 */
export const cleanKeywords = (keywords) => {
  if (!keywords || typeof keywords !== 'string') {
    return '';
  }

  return keywords
    .split(',')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
    .join(', ');
};

/**
 * Marketplace Product Schema Generator
 */

export const generateProductSchema = ({
  name,
  description,
  image,
  brand,
  price,
  currency = "USD",
  availability = "http://schema.org/InStock",
  url,
  seller,
  aggregateRating,
  reviews = []
}) => {
  // Only generate schema if required fields are present
  if (!name || !price) {
    return null;
  }

  const schema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": name,
    "description": description || "",
    "image": image || "",
    "offers": {
      "@type": "Offer",
      "price": price.toString(),
      "priceCurrency": currency,
      "availability": availability,
      "url": url || ""
    }
  };

  // Add brand if provided
  if (brand) {
    schema.brand = {
      "@type": "Brand",
      "name": brand
    };
  }

  // Add seller if provided
  if (seller) {
    schema.offers.seller = {
      "@type": "Organization",
      "name": seller.name || "",
      "url": seller.url || ""
    };
  }

  // Add aggregate rating if provided
  if (aggregateRating && aggregateRating.ratingValue && aggregateRating.reviewCount) {
    schema.aggregateRating = {
      "@type": "AggregateRating",
      "ratingValue": aggregateRating.ratingValue.toString(),
      "reviewCount": aggregateRating.reviewCount.toString()
    };
  }

  // Add reviews if provided (maximum 3)
  if (reviews && reviews.length > 0) {
    schema.review = reviews.slice(0, 3).map(review => ({
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": review.author || "Anonymous"
      },
      "datePublished": formatDateToISO(review.datePublished) || "",
      "reviewBody": review.reviewBody || "",
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating ? review.rating.toString() : "5"
      }
    }));
  }

  return schema;
};

/**
 * Category Page Schema Generators
 */

export const generateCollectionPageSchema = ({
  name,
  description,
  url,
  articles = [],
  currentPage = 1
}) => {
  // Only generate schema if required fields are present
  if (!name || !url) {
    return null;
  }

  const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;

  const schema = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": pageTitle,
    "description": description || "",
    "url": url
  };

  // Add articles as ListItem elements (maximum 10)
  if (articles && articles.length > 0) {
    schema.mainEntity = {
      "@type": "ItemList",
      "numberOfItems": articles.length,
      "itemListElement": articles.slice(0, 10).map((article, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": article.type === 'blog' ? "BlogPosting" : "Article",
          "@id": `https://www.tradereply.com/${article.type}/${article.slug}`,
          "name": article.title || "",
          "description": article.summary || "",
          "datePublished": formatDateToISO(article.created_at) || "",
          "author": {
            "@type": "Organization",
            "name": "TradeReply"
          }
        }
      }))
    };
  }

  return schema;
};

export const generateBreadcrumbListSchema = ({
  items = []
}) => {
  // Only generate schema if items are provided
  if (!items || items.length === 0) {
    return null;
  }

  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
};

/**
 * Utility Functions for Schema Generation
 */

/**
 * Select reviews based on average rating logic
 *
 * @param {Array} allReviews - All available reviews
 * @param {number} averageRating - Average rating (e.g., 4.2)
 * @param {number} maxReviews - Maximum number of reviews to select (default: 3)
 * @returns {Array} - Selected reviews
 */
export const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3) => {
  if (!allReviews || allReviews.length === 0) {
    return [];
  }

  // Round average rating to nearest integer for selection logic
  const targetRating = Math.round(averageRating);

  // Filter reviews by target rating
  const targetReviews = allReviews.filter(review =>
    Math.round(parseFloat(review.rating || 5)) === targetRating
  );

  // If we have enough reviews of the target rating, use them
  if (targetReviews.length >= maxReviews) {
    return shuffleArray(targetReviews).slice(0, maxReviews);
  }

  // If not enough target reviews, include nearby ratings
  const nearbyRatings = [targetRating, targetRating - 1, targetRating + 1].filter(r => r >= 1 && r <= 5);
  const nearbyReviews = allReviews.filter(review =>
    nearbyRatings.includes(Math.round(parseFloat(review.rating || 5)))
  );

  return shuffleArray(nearbyReviews).slice(0, maxReviews);
};

/**
 * Shuffle array utility function
 *
 * @param {Array} array - Array to shuffle
 * @returns {Array} - Shuffled array
 */
export const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Generate breadcrumb items for category pages
 *
 * @param {string} categoryName - Category name
 * @param {number} currentPage - Current page number (optional)
 * @returns {Array} - Breadcrumb items
 */
export const generateCategoryBreadcrumbs = (categoryName = "All Articles", currentPage = null) => {
  const breadcrumbs = [
    {
      name: "Home",
      url: "https://www.tradereply.com/"
    },
    {
      name: categoryName,
      url: "https://www.tradereply.com/category"
    }
  ];

  // Add page breadcrumb for paginated pages
  if (currentPage && currentPage > 1) {
    breadcrumbs.push({
      name: `Page ${currentPage}`,
      url: `https://www.tradereply.com/category/page/${currentPage}`
    });
  }

  return breadcrumbs;
};
